(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[103],{572:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>f});var t=a(5155),i=a(2115),l=a(5695),c=a(6874),r=a.n(c),m=a(7168),n=a(8482),d=a(7550),x=a(9799),h=a(4516),o=a(4186),j=a(7108),u=a(5339),p=a(646),N=a(9428);function v(e){let{events:s}=e;return(0,t.jsx)("div",{className:"space-y-4",children:s.map((e,a)=>(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsxs)("div",{className:"mr-4 flex flex-col items-center",children:["completed"===e.status?(0,t.jsx)("div",{className:"rounded-full bg-primary/20 p-1",children:(0,t.jsx)(p.A,{className:"h-5 w-5 text-primary"})}):"current"===e.status?(0,t.jsx)("div",{className:"rounded-full bg-blue-100 p-1",children:(0,t.jsx)(o.A,{className:"h-5 w-5 text-blue-600"})}):(0,t.jsx)("div",{className:"rounded-full border-2 border-gray-200 p-1",children:(0,t.jsx)(N.A,{className:"h-5 w-5 text-gray-300"})}),a<s.length-1&&(0,t.jsx)("div",{className:"h-full w-0.5 ".concat("completed"===e.status?"bg-primary/20":"bg-gray-200")})]}),(0,t.jsxs)("div",{className:"pb-8 pt-1",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-3 mb-1",children:[(0,t.jsx)("p",{className:"font-medium ".concat("completed"===e.status?"text-gray-900":"current"===e.status?"text-blue-600":"text-gray-500"),children:e.activity}),(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-500",children:[(0,t.jsx)("span",{children:e.date}),e.time&&(0,t.jsxs)("span",{className:"ml-1",children:["• ",e.time]})]})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.location})]})]},a))})}function g(e){let{trackingData:s}=e;return(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center",children:[(0,t.jsx)(j.A,{className:"mr-2 h-5 w-5 text-primary"}),"Shipment Details"]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-gray-500 mb-1",children:"Service Type"}),(0,t.jsx)("div",{className:"font-medium",children:s.service})]}),(0,t.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 text-primary mt-1 mr-2"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"From"}),(0,t.jsx)("div",{className:"font-medium",children:s.origin})]})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 text-primary mt-1 mr-2"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"To"}),(0,t.jsx)("div",{className:"font-medium",children:s.destination})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-gray-500 mb-1",children:"Weight"}),(0,t.jsx)("div",{className:"font-medium",children:s.weight})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-gray-500 mb-1",children:"Pieces"}),(0,t.jsx)("div",{className:"font-medium",children:s.pieces})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-gray-500 mb-1",children:"Reference Number"}),(0,t.jsx)("div",{className:"font-medium",children:s.reference})]})]})})]})}var y=a(1695);function f(){let e=(0,l.useSearchParams)(),[s,a]=(0,i.useState)("");(0,i.useEffect)(()=>{a(e.get("trackingNumber")||"")},[e]);let c={trackingNumber:s,status:"In Transit",estimatedDelivery:"May 25, 2025",origin:"Banjul, Gambia",destination:"Accra, Ghana",service:"Standard Air Freight",weight:"245 kg",pieces:"3",reference:"ORD-78945",currentLocation:"Lagos, Nigeria",lastUpdated:"May 20, 2025, 10:23 AM",events:[{date:"May 20, 2025",time:"10:23 AM",location:"Lagos, Nigeria",activity:"Departed facility",status:"completed"},{date:"May 19, 2025",time:"8:45 PM",location:"Lagos, Nigeria",activity:"Arrived at transit facility",status:"completed"},{date:"May 18, 2025",time:"2:30 PM",location:"Banjul, Gambia",activity:"Departed origin facility",status:"completed"},{date:"May 17, 2025",time:"11:15 AM",location:"Banjul, Gambia",activity:"Processed at origin facility",status:"completed"},{date:"May 16, 2025",time:"4:30 PM",location:"Banjul, Gambia",activity:"Shipment picked up",status:"completed"},{date:"May 15, 2025",time:"2:00 PM",location:"Banjul, Gambia",activity:"Shipment information received",status:"completed"}],nextSteps:[{date:"Estimated May 22, 2025",location:"Tema, Ghana",activity:"Arrival at destination facility",status:"upcoming"},{date:"Estimated May 23, 2025",location:"Accra, Ghana",activity:"Out for delivery",status:"upcoming"},{date:"Estimated May 25, 2025",location:"Accra, Ghana",activity:"Delivered",status:"upcoming"}]},p=[...c.events,...c.nextSteps].map(e=>({...e,status:e.status})).sort((e,s)=>{let a=new Date(e.date.replace("Estimated ",""));return new Date(s.date.replace("Estimated ","")).getTime()-a.getTime()});return(0,t.jsx)("div",{className:"min-h-screen flex flex-col",children:(0,t.jsxs)("main",{className:"flex-1",children:[(0,t.jsx)("section",{className:"bg-gradient-to-r from-gray-900 to-gray-800 text-white py-12",children:(0,t.jsx)("div",{className:"container px-4 md:px-6 mx-auto",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsx)(m.$,{variant:"outline",size:"sm",asChild:!0,className:"text-white border-white/20 hover:bg-white/10",children:(0,t.jsxs)(r(),{href:"/tracking",children:[(0,t.jsx)(d.A,{className:"mr-2 h-4 w-4"})," Back to Tracking"]})})}),(0,t.jsx)("h1",{className:"text-3xl font-bold mb-4",children:"Tracking Results"}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-6",children:[(0,t.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2",children:[(0,t.jsx)("span",{className:"text-sm text-gray-300",children:"Tracking Number:"}),(0,t.jsx)("span",{className:"ml-2 font-semibold",children:s})]}),(0,t.jsxs)("div",{className:"bg-primary/20 backdrop-blur-sm rounded-lg px-4 py-2 flex items-center",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Status:"}),(0,t.jsx)("span",{className:"ml-2 font-semibold",children:c.status})]})]}),(0,t.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-6",children:[(0,t.jsx)("p",{className:"text-lg mb-4",children:"Track another shipment:"}),(0,t.jsx)(y.default,{})]})]})})}),(0,t.jsx)("section",{className:"py-12 bg-gray-50",children:(0,t.jsx)("div",{className:"container px-4 md:px-6 mx-auto",children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center",children:[(0,t.jsx)(x.A,{className:"mr-2 h-5 w-5 text-primary"}),"Shipment Progress"]})}),(0,t.jsxs)(n.Wu,{children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(h.A,{className:"h-5 w-5 text-gray-500 mr-2"}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Current Location:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:c.currentLocation})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(o.A,{className:"h-5 w-5 text-gray-500 mr-2"}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Last Updated:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:c.lastUpdated})]})]}),(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)(j.A,{className:"h-5 w-5 text-gray-500 mr-2"}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Estimated Delivery:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:c.estimatedDelivery})]})]}),(0,t.jsx)(v,{events:p})]})]})}),(0,t.jsxs)("div",{children:[(0,t.jsx)(g,{trackingData:c}),(0,t.jsx)("div",{className:"mt-6",children:(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"text-lg flex items-center",children:[(0,t.jsx)(u.A,{className:"mr-2 h-5 w-5 text-primary"}),"Need Help?"]})}),(0,t.jsxs)(n.Wu,{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"If you have questions about your shipment, our customer service team is here to help."}),(0,t.jsx)(m.$,{asChild:!0,className:"w-full",children:(0,t.jsx)(r(),{href:"/contact",children:"Contact Support"})})]})]})})]})]})})})})]})})}},4459:(e,s,a)=>{Promise.resolve().then(a.bind(a,572))}},e=>{var s=s=>e(e.s=s);e.O(0,[96,76,358],()=>s(4459)),_N_E=e.O()}]);