{"version": 2, "framework": "nextjs", "buildCommand": "npm run build:vercel", "installCommand": "npm ci", "functions": {"app/api/**/*.ts": {"runtime": "nodejs18.x"}}, "env": {"DEPLOYMENT_TYPE": "vercel", "NEXT_PUBLIC_DEPLOYMENT_TYPE": "vercel"}, "build": {"env": {"DEPLOYMENT_TYPE": "vercel", "NEXT_PUBLIC_DEPLOYMENT_TYPE": "vercel"}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}], "redirects": [{"source": "/admin", "destination": "/admin/dashboard", "permanent": true}, {"source": "/account", "destination": "/account/dashboard", "permanent": true}]}