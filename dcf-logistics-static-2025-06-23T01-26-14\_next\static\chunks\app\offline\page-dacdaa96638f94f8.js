(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[734],{3697:(e,s,a)=>{"use strict";a.d(s,{default:()=>m});var l=a(5155),c=a(6874),i=a.n(c),n=a(7168),r=a(8482),t=a(4449),d=a(9799),x=a(7340),h=a(9420),j=a(3904),o=a(6517);function m(){return(0,l.jsx)("div",{className:"container mx-auto px-4 py-8 min-h-screen flex items-center justify-center",children:(0,l.jsxs)("div",{className:"max-w-2xl mx-auto text-center space-y-8",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("div",{className:"flex justify-center",children:(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(t.A,{className:"h-24 w-24 text-gray-400"}),(0,l.jsx)("div",{className:"absolute -top-2 -right-2 bg-red-500 rounded-full p-2",children:(0,l.jsx)(t.A,{className:"h-4 w-4 text-white"})})]})}),(0,l.jsx)("h1",{className:"text-4xl font-bold text-gray-900",children:"You're Offline"}),(0,l.jsx)("p",{className:"text-xl text-gray-600",children:"No internet connection detected. Don't worry, some features are still available!"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)(r.Zp,{children:[(0,l.jsxs)(r.aR,{children:[(0,l.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(d.A,{className:"h-5 w-5"}),"Cached Tracking"]}),(0,l.jsx)(r.BT,{children:"View recently accessed tracking information"})]}),(0,l.jsx)(r.Wu,{children:(0,l.jsx)(n.$,{asChild:!0,className:"w-full",children:(0,l.jsx)(i(),{href:"/tracking",children:"View Tracking"})})})]}),(0,l.jsxs)(r.Zp,{children:[(0,l.jsxs)(r.aR,{children:[(0,l.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(x.A,{className:"h-5 w-5"}),"Cached Pages"]}),(0,l.jsx)(r.BT,{children:"Browse previously visited pages"})]}),(0,l.jsx)(r.Wu,{children:(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(n.$,{asChild:!0,variant:"outline",className:"w-full",children:(0,l.jsx)(i(),{href:"/services",children:"Services"})}),(0,l.jsx)(n.$,{asChild:!0,variant:"outline",className:"w-full",children:(0,l.jsx)(i(),{href:"/about",children:"About Us"})})]})})]}),(0,l.jsxs)(r.Zp,{children:[(0,l.jsxs)(r.aR,{children:[(0,l.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(h.A,{className:"h-5 w-5"}),"Contact Info"]}),(0,l.jsx)(r.BT,{children:"Get in touch when you're back online"})]}),(0,l.jsx)(r.Wu,{children:(0,l.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,l.jsx)("p",{children:"\uD83D\uDCDE +220 123 456 789"}),(0,l.jsx)("p",{children:"✉️ <EMAIL>"}),(0,l.jsx)("p",{children:"\uD83D\uDCCD 123 Logistics Avenue, Banjul"})]})})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)(n.$,{onClick:()=>window.location.reload(),className:"flex items-center gap-2",size:"lg",children:[(0,l.jsx)(j.A,{className:"h-4 w-4"}),"Try Again"]}),(0,l.jsxs)("div",{className:"flex items-center justify-center gap-2 text-sm text-gray-500",children:[(0,l.jsx)(o.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{children:"Connection will be restored automatically when available"})]})]})]})})}},9218:(e,s,a)=>{Promise.resolve().then(a.bind(a,3697))}},e=>{var s=s=>e(e.s=s);e.O(0,[96,76,358],()=>s(9218)),_N_E=e.O()}]);