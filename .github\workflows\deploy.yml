name: DCF Logistics - Automated Deployment

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'

jobs:
  # Test and validate builds
  test-builds:
    name: Test Build Configurations
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        deployment-type: [vercel, static]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Validate environment for ${{ matrix.deployment-type }}
        run: node deployment-config.js validate ${{ matrix.deployment-type }}
        env:
          NEXT_PUBLIC_SITE_URL: https://dcflogistics.com
          NEXT_PUBLIC_CONTACT_EMAIL: <EMAIL>
          DATABASE_URL: postgresql://test:test@localhost:5432/test
          NEXTAUTH_SECRET: test-secret-key-32-characters-long
          NEXTAUTH_URL: https://dcflogistics.com
      
      - name: Test build for ${{ matrix.deployment-type }}
        run: node deployment-config.js build ${{ matrix.deployment-type }}
        env:
          NEXT_PUBLIC_SITE_URL: https://dcflogistics.com
          NEXT_PUBLIC_CONTACT_EMAIL: <EMAIL>
          DATABASE_URL: postgresql://test:test@localhost:5432/test
          NEXTAUTH_SECRET: test-secret-key-32-characters-long
          NEXTAUTH_URL: https://dcflogistics.com

  # Deploy to Vercel (Production)
  deploy-vercel:
    name: Deploy to Vercel
    runs-on: ubuntu-latest
    needs: test-builds
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'

  # Deploy to Vercel (Preview)
  deploy-vercel-preview:
    name: Deploy Preview to Vercel
    runs-on: ubuntu-latest
    needs: test-builds
    if: github.event_name == 'pull_request'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Deploy Preview to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}

  # Build static version for shared hosting
  build-static:
    name: Build Static Version
    runs-on: ubuntu-latest
    needs: test-builds
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Create static deployment package
        run: npm run package:static
        env:
          NEXT_PUBLIC_SITE_URL: https://dcflogistics.com
          NEXT_PUBLIC_CONTACT_EMAIL: <EMAIL>

      - name: Upload static build artifact
        uses: actions/upload-artifact@v4
        with:
          name: static-deployment-package
          path: dcf-logistics-static-*.zip
          retention-days: 30

      - name: Upload package directory
        uses: actions/upload-artifact@v4
        with:
          name: static-deployment-files
          path: dcf-logistics-static-*/
          retention-days: 30

  # Notify deployment status
  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [deploy-vercel, build-static]
    if: always()
    
    steps:
      - name: Notify Success
        if: needs.deploy-vercel.result == 'success' && needs.build-static.result == 'success'
        run: |
          echo "✅ Deployment successful!"
          echo "🚀 Vercel: Deployed successfully"
          echo "📦 Static: Build artifact created"
      
      - name: Notify Failure
        if: needs.deploy-vercel.result == 'failure' || needs.build-static.result == 'failure'
        run: |
          echo "❌ Deployment failed!"
          echo "Please check the logs for details."
