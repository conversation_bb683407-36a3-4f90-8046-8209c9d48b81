{"name": "dcf-logistics-static", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rimraf .next && rimraf out", "build:vercel": "node scripts/vercel-build.js", "build:static": "node deployment-config.js build static", "build:godaddy": "node deployment-config.js build static", "deploy:vercel": "node scripts/deploy.js deploy vercel", "deploy:vercel-preview": "node scripts/deploy.js deploy vercel-preview", "deploy:static": "node scripts/deploy.js deploy static", "deploy:netlify": "node scripts/deploy.js deploy netlify", "package:static": "node scripts/create-static-package.js", "package:vercel": "node scripts/deploy.js package vercel", "package:godaddy": "node scripts/create-static-package.js", "config:switch-vercel": "node deployment-config.js switch vercel", "config:switch-static": "node deployment-config.js switch static", "config:validate-vercel": "node deployment-config.js validate vercel", "config:validate-static": "node deployment-config.js validate static", "test:build-all": "npm run build:vercel && npm run build:static", "setup:vercel": "node scripts/setup-vercel.js", "setup:database": "node scripts/setup-database.js setup", "db:providers": "node scripts/setup-database.js providers", "db:instructions": "node scripts/setup-database.js instructions", "db:test": "node scripts/setup-database.js test", "db:migrate": "node scripts/setup-database.js migrate", "db:seed": "node scripts/setup-database.js seed", "status": "node scripts/deployment-status.js", "deployment:status": "node scripts/deployment-status.js", "verify": "node scripts/verify-deployment.js", "test:deployment": "node scripts/verify-deployment.js"}, "dependencies": {"@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "eslint": "^9.29.0", "eslint-config-next": "^15.3.4", "framer-motion": "^11.15.0", "lucide-react": "^0.468.0", "next": "^15.1.0", "next-auth": "^4.24.11", "next-themes": "^0.4.4", "react": "^19.0.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.0", "recharts": "^2.13.3", "sonner": "^1.7.4", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8", "rimraf": "^6.0.1", "tailwindcss": "^3.4.17", "typescript": "^5"}, "description": "A comprehensive, enterprise-grade logistics management platform built with Next.js 15, featuring real-time tracking, payment processing, advanced analytics, and complete business automation.", "main": "deployment-config.js", "directories": {"lib": "lib"}, "repository": {"type": "git", "url": "git+https://github.com/ejanneh18/dcf-logistics.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/ejanneh18/dcf-logistics/issues"}, "homepage": "https://github.com/ejanneh18/dcf-logistics#readme"}