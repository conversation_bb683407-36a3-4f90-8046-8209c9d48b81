(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5],{7444:(e,s,a)=>{Promise.resolve().then(a.bind(a,9278))},9278:(e,s,a)=>{"use strict";a.d(s,{default:()=>R});var i=a(5155),t=a(2115),n=a(7168),r=a(8482),l=a(9852),c=a(2714),o=a(9474),d=a(4059),m=a(9428),h=a(3999);let u=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,i.jsx)(d.bL,{className:(0,h.cn)("grid gap-2",a),...t,ref:s})});u.displayName=d.bL.displayName;let x=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,i.jsx)(d.q7,{ref:s,className:(0,h.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),...t,children:(0,i.jsx)(d.C1,{className:"flex items-center justify-center",children:(0,i.jsx)(m.A,{className:"h-2.5 w-2.5 fill-current text-current"})})})});x.displayName=d.q7.displayName;var p=a(5784),g=a(5139),j=a(6671),v=a(646),f=a(9074),y=a(1154),N=a(3013),b=a(2355),w=a(3052),C=a(3900);function S(e){let{className:s,classNames:a,showOutsideDays:t=!0,...r}=e;return(0,i.jsx)(C.hv,{showOutsideDays:t,className:(0,h.cn)("p-3",s),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,h.cn)((0,n.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,h.cn)((0,n.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...a},components:{IconLeft:e=>{let{...s}=e;return(0,i.jsx)(b.A,{className:"h-4 w-4"})},IconRight:e=>{let{...s}=e;return(0,i.jsx)(w.A,{className:"h-4 w-4"})}},...r})}S.displayName="Calendar";var q=a(547);let D=q.bL,F=q.l9,k=t.forwardRef((e,s)=>{let{className:a,align:t="center",sideOffset:n=4,...r}=e;return(0,i.jsx)(q.ZL,{children:(0,i.jsx)(q.UC,{ref:s,align:t,sideOffset:n,className:(0,h.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...r})})});function R(){let[e,s]=(0,t.useState)(!1),[a,d]=(0,t.useState)(!1),[m,b]=(0,t.useState)(""),[w,C]=(0,t.useState)({fullName:"",email:"",phone:"",company:"",serviceType:"",origin:"",destination:"",shipmentDate:void 0,cargoType:"",cargoDetails:"",dimensions:"",weight:"",specialRequirements:"",additionalServices:[],estimatedValue:""}),q=e=>{let{name:s,value:a}=e.target;C(e=>({...e,[s]:a}))},R=(e,s)=>{C(a=>({...a,[e]:s}))},T=(e,s)=>{C(a=>s?{...a,additionalServices:[...a.additionalServices,e]}:{...a,additionalServices:a.additionalServices.filter(s=>s!==e)})},J=async e=>{e.preventDefault(),s(!0);try{let e="Quote Request - ".concat(w.serviceType),a="Quote Request Details:\n\nContact Information:\n- Name: ".concat(w.fullName,"\n- Email: ").concat(w.email,"\n- Phone: ").concat(w.phone,"\n- Company: ").concat(w.company||"Not provided","\n\nService Details:\n- Service Type: ").concat(w.serviceType,"\n- Origin: ").concat(w.origin,"\n- Destination: ").concat(w.destination,"\n- Shipment Date: ").concat(w.shipmentDate?(0,N.GP)(w.shipmentDate,"PPP"):"Not specified","\n\nCargo Information:\n- Cargo Type: ").concat(w.cargoType,"\n- Description: ").concat(w.cargoDetails,"\n- Dimensions: ").concat(w.dimensions||"Not specified","\n- Weight: ").concat(w.weight||"Not specified","\n- Estimated Value: ").concat(w.estimatedValue?"$".concat(w.estimatedValue," USD"):"Not specified","\n\nAdditional Services:\n").concat(w.additionalServices.length>0?w.additionalServices.map(e=>"- ".concat(e)).join("\n"):"- None selected","\n\nSpecial Requirements:\n").concat(w.specialRequirements||"None","\n\nPlease provide a detailed quote for the above requirements."),i="mailto:<EMAIL>?subject=".concat(encodeURIComponent(e),"&body=").concat(encodeURIComponent(a));window.open(i,"_blank");let t="DCF-".concat(Date.now().toString().slice(-6));setTimeout(()=>{d(!0),b(t),j.oR.success("Email client opened! Please send the email to receive your quote."),C({fullName:"",email:"",phone:"",company:"",serviceType:"",origin:"",destination:"",shipmentDate:void 0,cargoType:"",cargoDetails:"",dimensions:"",weight:"",specialRequirements:"",additionalServices:[],estimatedValue:""}),s(!1)},1e3)}catch(e){console.error("Quote request error:",e),j.oR.error("Failed to open email client. Please try again or contact us directly."),s(!1)}};return a?(0,i.jsxs)(r.Zp,{className:"w-full",children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsx)(r.ZB,{className:"text-center",children:"Quote Request Submitted!"}),(0,i.jsx)(r.BT,{className:"text-center",children:"Your quote request has been successfully submitted."})]}),(0,i.jsxs)(r.Wu,{className:"text-center space-y-6",children:[(0,i.jsx)(v.A,{className:"h-16 w-16 text-green-500 mx-auto"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-green-600",children:"Thank you for your quote request!"}),(0,i.jsxs)("p",{className:"text-gray-600",children:["Quote Reference: ",(0,i.jsx)("span",{className:"font-mono font-bold",children:m})]})]}),(0,i.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,i.jsx)("h4",{className:"font-semibold text-green-800 mb-2",children:"What happens next?"}),(0,i.jsxs)("ul",{className:"text-sm text-green-700 space-y-1 text-left",children:[(0,i.jsx)("li",{children:"• You'll receive a detailed quote via email within 2-4 hours"}),(0,i.jsx)("li",{children:"• Our sales team will review your requirements"}),(0,i.jsx)("li",{children:"• We'll contact you if we need any additional information"}),(0,i.jsx)("li",{children:"• The quote will be valid for 30 days"})]})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:["For urgent requests, please call us at"," ",(0,i.jsx)("a",{href:"tel:+2201234567",className:"text-primary font-semibold hover:underline",children:"+************"})]}),(0,i.jsx)(n.$,{onClick:()=>{d(!1),b("")},variant:"outline",children:"Submit Another Quote Request"})]})]})]}):(0,i.jsxs)(r.Zp,{className:"w-full",children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsx)(r.ZB,{children:"Request a Quote"}),(0,i.jsx)(r.BT,{children:"Fill out the form below with your shipping details to receive a customized quote."})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsxs)("form",{onSubmit:J,className:"space-y-6",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"text-lg font-medium",children:"Contact Information"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(c.J,{htmlFor:"fullName",children:"Full Name *"}),(0,i.jsx)(l.p,{id:"fullName",name:"fullName",value:w.fullName,onChange:q,required:!0})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(c.J,{htmlFor:"email",children:"Email Address *"}),(0,i.jsx)(l.p,{id:"email",name:"email",type:"email",value:w.email,onChange:q,required:!0})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(c.J,{htmlFor:"phone",children:"Phone Number *"}),(0,i.jsx)(l.p,{id:"phone",name:"phone",value:w.phone,onChange:q,required:!0})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(c.J,{htmlFor:"company",children:"Company Name"}),(0,i.jsx)(l.p,{id:"company",name:"company",value:w.company,onChange:q})]})]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"text-lg font-medium",children:"Service Details"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(c.J,{htmlFor:"serviceType",children:"Service Type *"}),(0,i.jsxs)(p.l6,{value:w.serviceType,onValueChange:e=>R("serviceType",e),required:!0,children:[(0,i.jsx)(p.bq,{id:"serviceType",children:(0,i.jsx)(p.yv,{placeholder:"Select service type"})}),(0,i.jsxs)(p.gC,{children:[(0,i.jsx)(p.eb,{value:"freight-forwarding",children:"Freight Forwarding"}),(0,i.jsx)(p.eb,{value:"customs-brokerage",children:"Customs Brokerage"}),(0,i.jsx)(p.eb,{value:"warehousing",children:"Warehousing & Distribution"}),(0,i.jsx)(p.eb,{value:"air-freight",children:"Air Freight"}),(0,i.jsx)(p.eb,{value:"ocean-freight",children:"Ocean Freight"}),(0,i.jsx)(p.eb,{value:"road-transport",children:"Road Transportation"}),(0,i.jsx)(p.eb,{value:"cross-border",children:"Cross Border Logistics"}),(0,i.jsx)(p.eb,{value:"other",children:"Other"})]})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(c.J,{htmlFor:"origin",children:"Origin Location *"}),(0,i.jsx)(l.p,{id:"origin",name:"origin",value:w.origin,onChange:q,placeholder:"City, Country",required:!0})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(c.J,{htmlFor:"destination",children:"Destination Location *"}),(0,i.jsx)(l.p,{id:"destination",name:"destination",value:w.destination,onChange:q,placeholder:"City, Country",required:!0})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(c.J,{htmlFor:"shipmentDate",children:"Estimated Shipment Date"}),(0,i.jsxs)(D,{children:[(0,i.jsx)(F,{asChild:!0,children:(0,i.jsxs)(n.$,{variant:"outline",className:(0,h.cn)("w-full justify-start text-left font-normal",!w.shipmentDate&&"text-muted-foreground"),children:[(0,i.jsx)(f.A,{className:"mr-2 h-4 w-4"}),w.shipmentDate?(0,N.GP)(w.shipmentDate,"PPP"):"Select a date"]})}),(0,i.jsx)(k,{className:"w-auto p-0",children:(0,i.jsx)(S,{mode:"single",selected:w.shipmentDate,onSelect:e=>{C(s=>({...s,shipmentDate:e}))},initialFocus:!0})})]})]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"text-lg font-medium",children:"Cargo Information"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(c.J,{children:"Cargo Type *"}),(0,i.jsx)(u,{value:w.cargoType,onValueChange:e=>R("cargoType",e),required:!0,children:(0,i.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(x,{value:"general",id:"general"}),(0,i.jsx)(c.J,{htmlFor:"general",children:"General Cargo"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(x,{value:"hazardous",id:"hazardous"}),(0,i.jsx)(c.J,{htmlFor:"hazardous",children:"Hazardous Materials"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(x,{value:"perishable",id:"perishable"}),(0,i.jsx)(c.J,{htmlFor:"perishable",children:"Perishable Goods"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(x,{value:"oversized",id:"oversized"}),(0,i.jsx)(c.J,{htmlFor:"oversized",children:"Oversized Cargo"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(x,{value:"valuable",id:"valuable"}),(0,i.jsx)(c.J,{htmlFor:"valuable",children:"High-Value Items"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(x,{value:"other",id:"other-cargo"}),(0,i.jsx)(c.J,{htmlFor:"other-cargo",children:"Other"})]})]})})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(c.J,{htmlFor:"cargoDetails",children:"Cargo Description *"}),(0,i.jsx)(o.T,{id:"cargoDetails",name:"cargoDetails",value:w.cargoDetails,onChange:q,placeholder:"Please describe your cargo in detail",required:!0})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(c.J,{htmlFor:"dimensions",children:"Dimensions"}),(0,i.jsx)(l.p,{id:"dimensions",name:"dimensions",value:w.dimensions,onChange:q,placeholder:"Length x Width x Height (cm)"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(c.J,{htmlFor:"weight",children:"Weight"}),(0,i.jsx)(l.p,{id:"weight",name:"weight",value:w.weight,onChange:q,placeholder:"Total weight (kg)"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(c.J,{htmlFor:"estimatedValue",children:"Estimated Value (USD)"}),(0,i.jsx)(l.p,{id:"estimatedValue",name:"estimatedValue",type:"number",min:"0",step:"0.01",value:w.estimatedValue,onChange:q,placeholder:"0.00"})]})]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"text-lg font-medium",children:"Additional Services"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(g.S,{id:"insurance",checked:w.additionalServices.includes("insurance"),onCheckedChange:e=>T("insurance",e)}),(0,i.jsx)(c.J,{htmlFor:"insurance",children:"Cargo Insurance"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(g.S,{id:"customs",checked:w.additionalServices.includes("customs"),onCheckedChange:e=>T("customs",e)}),(0,i.jsx)(c.J,{htmlFor:"customs",children:"Customs Clearance"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(g.S,{id:"packaging",checked:w.additionalServices.includes("packaging"),onCheckedChange:e=>T("packaging",e)}),(0,i.jsx)(c.J,{htmlFor:"packaging",children:"Packaging Services"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(g.S,{id:"warehousing",checked:w.additionalServices.includes("warehousing"),onCheckedChange:e=>T("warehousing",e)}),(0,i.jsx)(c.J,{htmlFor:"warehousing",children:"Warehousing"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(g.S,{id:"door-to-door",checked:w.additionalServices.includes("door-to-door"),onCheckedChange:e=>T("door-to-door",e)}),(0,i.jsx)(c.J,{htmlFor:"door-to-door",children:"Door-to-Door Delivery"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(g.S,{id:"tracking",checked:w.additionalServices.includes("tracking"),onCheckedChange:e=>T("tracking",e)}),(0,i.jsx)(c.J,{htmlFor:"tracking",children:"Advanced Tracking"})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(c.J,{htmlFor:"specialRequirements",children:"Special Requirements or Instructions"}),(0,i.jsx)(o.T,{id:"specialRequirements",name:"specialRequirements",value:w.specialRequirements,onChange:q,placeholder:"Any special handling instructions or requirements"})]}),(0,i.jsx)(n.$,{type:"submit",className:"w-full",disabled:e,children:e?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(y.A,{className:"mr-2 h-4 w-4 animate-spin"})," Processing..."]}):"Submit Quote Request"})]})})]})}k.displayName=q.UC.displayName}},e=>{var s=s=>e(e.s=s);e.O(0,[96,76,358],()=>s(7444)),_N_E=e.O()}]);