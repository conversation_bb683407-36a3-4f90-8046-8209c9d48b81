(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[76],{582:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6874,23))},1695:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var s=r(5155),a=r(2115),i=r(5695),n=r(7168),l=r(9852),d=r(3580),o=r(7924);function c(){let[e,t]=(0,a.useState)(""),[r,c]=(0,a.useState)(!1),u=(0,i.useRouter)(),m=async t=>{if(t.preventDefault(),!e.trim()){(0,d.oR)({title:"Tracking number required",description:"Please enter a valid tracking number",variant:"destructive"});return}c(!0);try{u.push("/tracking/results?trackingNumber=".concat(encodeURIComponent(e)))}catch(e){(0,d.oR)({title:"Error",description:"An error occurred while processing your request. Please try again.",variant:"destructive"}),c(!1)}};return(0,s.jsx)("form",{onSubmit:m,className:"w-full",children:(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)(l.p,{type:"text",placeholder:"Enter your tracking number",value:e,onChange:e=>t(e.target.value),className:"bg-white text-gray-900 h-12 border-0",disabled:r})}),(0,s.jsx)(n.$,{type:"submit",disabled:r,className:"h-12 px-6",children:r?"Tracking...":(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.A,{className:"mr-2 h-4 w-4"})," Track Shipment"]})})]})})}},2714:(e,t,r)=>{"use strict";r.d(t,{J:()=>o});var s=r(5155),a=r(2115),i=r(968),n=r(2085),l=r(3999);let d=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.b,{ref:t,className:(0,l.cn)(d(),r),...a})});o.displayName=i.b.displayName},3580:(e,t,r)=>{"use strict";r.d(t,{dj:()=>m,oR:()=>u});var s=r(2115);let a=0,i=new Map,n=e=>{if(i.has(e))return;let t=setTimeout(()=>{i.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);i.set(e,t)},l=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?n(r):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[],o={toasts:[]};function c(e){o=l(o,e),d.forEach(e=>{e(o)})}function u(e){let{...t}=e,r=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>c({type:"DISMISS_TOAST",toastId:r});return c({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||s()}}}),{id:r,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function m(){let[e,t]=s.useState(o);return s.useEffect(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},3999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var s=r(2596),a=r(9688);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},4441:()=>{},5139:(e,t,r)=>{"use strict";r.d(t,{S:()=>d});var s=r(5155),a=r(2115),i=r(6981),n=r(5196),l=r(3999);let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.bL,{ref:t,className:(0,l.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",r),...a,children:(0,s.jsx)(i.C1,{className:(0,l.cn)("flex items-center justify-center text-current"),children:(0,s.jsx)(n.A,{className:"h-4 w-4"})})})});d.displayName=i.bL.displayName},5217:(e,t,r)=>{Promise.resolve().then(r.bind(r,9418)),Promise.resolve().then(r.bind(r,8375)),Promise.resolve().then(r.t.bind(r,6874,23))},5784:(e,t,r)=>{"use strict";r.d(t,{bq:()=>m,eb:()=>h,gC:()=>x,l6:()=>c,yv:()=>u});var s=r(5155),a=r(2115),i=r(4582),n=r(6474),l=r(7863),d=r(5196),o=r(3999);let c=i.bL;i.YJ;let u=i.WT,m=a.forwardRef((e,t)=>{let{className:r,children:a,...l}=e;return(0,s.jsxs)(i.l9,{ref:t,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",r),...l,children:[a,(0,s.jsx)(i.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=i.l9.displayName;let f=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.PP,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",r),...a,children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})});f.displayName=i.PP.displayName;let p=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.wn,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",r),...a,children:(0,s.jsx)(n.A,{className:"h-4 w-4"})})});p.displayName=i.wn.displayName;let x=a.forwardRef((e,t)=>{let{className:r,children:a,position:n="popper",...l}=e;return(0,s.jsx)(i.ZL,{children:(0,s.jsxs)(i.UC,{ref:t,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:n,...l,children:[(0,s.jsx)(f,{}),(0,s.jsx)(i.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,s.jsx)(p,{})]})})});x.displayName=i.UC.displayName,a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.JU,{ref:t,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",r),...a})}).displayName=i.JU.displayName;let h=a.forwardRef((e,t)=>{let{className:r,children:a,...n}=e;return(0,s.jsxs)(i.q7,{ref:t,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...n,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(i.VF,{children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})}),(0,s.jsx)(i.p4,{children:a})]})});h.displayName=i.q7.displayName,a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.wv,{ref:t,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",r),...a})}).displayName=i.wv.displayName},6462:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6874,23)),Promise.resolve().then(r.t.bind(r,3063,23))},7168:(e,t,r)=>{"use strict";r.d(t,{$:()=>o,r:()=>d});var s=r(5155),a=r(2115),i=r(9708),n=r(2085),l=r(3999);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground text-primary",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef((e,t)=>{let{className:r,variant:a,size:n,asChild:o=!1,...c}=e,u=o?i.DX:"button";return(0,s.jsx)(u,{className:(0,l.cn)(d({variant:a,size:n,className:r})),ref:t,...c})});o.displayName="Button"},8375:(e,t,r)=>{"use strict";r.d(t,{default:()=>f});var s=r(5155),a=r(2115),i=r(3478),n=r(6474),l=r(3999);let d=i.bL,o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.q7,{ref:t,className:(0,l.cn)("border-b",r),...a})});o.displayName="AccordionItem";let c=a.forwardRef((e,t)=>{let{className:r,children:a,...d}=e;return(0,s.jsx)(i.Y9,{className:"flex",children:(0,s.jsxs)(i.l9,{ref:t,className:(0,l.cn)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",r),...d,children:[a,(0,s.jsx)(n.A,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})})});c.displayName=i.l9.displayName;let u=a.forwardRef((e,t)=>{let{className:r,children:a,...n}=e;return(0,s.jsx)(i.UC,{ref:t,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...n,children:(0,s.jsx)("div",{className:(0,l.cn)("pb-4 pt-0",r),children:a})})});u.displayName=i.UC.displayName;var m=r(8482);function f(e){let{title:t="Frequently Asked Questions",description:r="Find answers to common questions about our services",faqs:a}=e;return(0,s.jsxs)(m.Zp,{className:"border-none shadow-none",children:[(0,s.jsxs)(m.aR,{className:"text-center",children:[(0,s.jsx)(m.ZB,{className:"text-3xl font-bold tracking-tight text-gray-900",children:t}),r&&(0,s.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto mt-4",children:r})]}),(0,s.jsx)(m.Wu,{children:(0,s.jsx)(d,{type:"single",collapsible:!0,className:"w-full",children:a.map((e,t)=>(0,s.jsxs)(o,{value:"item-".concat(t),children:[(0,s.jsx)(c,{className:"text-left font-medium text-lg",children:e.question}),(0,s.jsx)(u,{className:"text-gray-600",children:(0,s.jsx)("div",{className:"pt-2 pb-4",children:e.answer})})]},t))})})]})}},8482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>n,aR:()=>l,wL:()=>u});var s=r(5155),a=r(2115),i=r(3999);let n=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});n.displayName="Card";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",r),...a})});l.displayName="CardHeader";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});d.displayName="CardTitle";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",r),...a})});o.displayName="CardDescription";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",r),...a})});c.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",r),...a})});u.displayName="CardFooter"},9418:(e,t,r)=>{"use strict";r.d(t,{default:()=>m});var s=r(5155),a=r(2115),i=r(7168),n=r(8482),l=r(9852),d=r(2714),o=r(9474),c=r(5784),u=r(3580);function m(e){let{service:t}=e,[r,m]=(0,a.useState)(!1),f=async e=>{e.preventDefault(),m(!0),setTimeout(()=>{m(!1),(0,u.oR)({title:"Form submitted successfully",description:"We've received your inquiry about ".concat(t,". Our team will contact you shortly.")}),e.target.reset()},1500)};return(0,s.jsx)(n.Zp,{children:(0,s.jsx)(n.Wu,{className:"p-6",children:(0,s.jsxs)("form",{onSubmit:f,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"name",children:"Full Name"}),(0,s.jsx)(l.p,{id:"name",placeholder:"John Doe",required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"email",children:"Email Address"}),(0,s.jsx)(l.p,{id:"email",type:"email",placeholder:"<EMAIL>",required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"phone",children:"Phone Number"}),(0,s.jsx)(l.p,{id:"phone",placeholder:"+220 123 456 789"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"inquiry-type",children:"Inquiry Type"}),(0,s.jsxs)(c.l6,{defaultValue:"quote",children:[(0,s.jsx)(c.bq,{id:"inquiry-type",children:(0,s.jsx)(c.yv,{placeholder:"Select inquiry type"})}),(0,s.jsxs)(c.gC,{children:[(0,s.jsx)(c.eb,{value:"quote",children:"Request a Quote"}),(0,s.jsx)(c.eb,{value:"information",children:"General Information"}),(0,s.jsx)(c.eb,{value:"support",children:"Customer Support"}),(0,s.jsx)(c.eb,{value:"other",children:"Other"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"message",children:"Message"}),(0,s.jsx)(o.T,{id:"message",placeholder:"Please provide details about your service requirements...",className:"min-h-[120px]",required:!0})]}),(0,s.jsx)(i.$,{type:"submit",className:"w-full",disabled:r,children:r?"Submitting...":"Submit Inquiry"})]})})})}},9474:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});var s=r(5155),a=r(2115),i=r(3999);let n=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...a})});n.displayName="Textarea"},9647:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(9509);function a(e){let{src:t,width:r,quality:a}=e;return s.env.PORTABLE_DEPLOYMENT,t}},9852:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(5155),a=r(2115),i=r(3999);let n=a.forwardRef((e,t)=>{let{className:r,type:a,...n}=e;return(0,s.jsx)("input",{type:a,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...n})});n.displayName="Input"}}]);